from typing import Any

from langchain_core.messages import AIMessage
from langfuse import Lang<PERSON>
from langfuse.model import CreateScore

from app.evaluation.pipelines.base import BaseEvaluationPipeline


class LangfuseToolSequenceEvaluationPipeline(BaseEvaluationPipeline):
    """
    Langfuse evaluation pipeline for evaluating tool call sequences in LangGraph responses.
    """

    def __init__(self, langfuse_client: Langfuse):
        super().__init__("langfuse_tool_sequence_evaluation")
        self.langfuse = langfuse_client

    async def run_evaluation(self, dataset_name: str) -> dict[str, Any]:
        """
        Run the evaluation pipeline on a Langfuse dataset.

        Args:
            dataset_name: Name of the <PERSON>fuse dataset to evaluate against

        Returns:
            Dictionary containing evaluation results and metrics
        """
        self.logger.info(f"Starting evaluation on dataset: {dataset_name}")

        dataset = self.langfuse.get_dataset(dataset_name)
        results = {
            "dataset_name": dataset_name,
            "total_items": 0,
            "evaluated_items": 0,
            "average_score": 0.0,
            "scores": [],
            "errors": [],
        }

        total_score = 0.0
        evaluated_count = 0

        for item in dataset.items:
            try:
                results["total_items"] += 1

                # Get Langchain handler for this evaluation run
                handler = item.get_langchain_handler(
                    run_name=f"{self.pipeline_name}-run"
                )

                # TODO: Replace this mock with actual graph invocation
                # This is where you would invoke your LangGraph application
                # response = await self._invoke_graph(item.input, handler)
                response = self._create_mock_response()

                # Extract actual sequence from response
                actual_sequence = self.extract_sequence(response)

                # Get expected sequence from dataset item
                expected_sequence = item.expected_output

                # Evaluate the sequence
                score_data = self.evaluate_sequence(actual_sequence, expected_sequence)

                # Log score to Langfuse
                trace_id = handler.get_trace_id()
                self.langfuse.score(
                    CreateScore(
                        trace_id=trace_id,
                        name=score_data["name"],
                        value=score_data["score"],
                        comment=score_data["comment"],
                    )
                )

                # Track results
                total_score += score_data["score"]
                evaluated_count += 1
                results["evaluated_items"] += 1
                results["scores"].append(
                    {
                        "input": item.input,
                        "score": score_data["score"],
                        "comment": score_data["comment"],
                    }
                )

                self.logger.info(
                    f"Input: '{item.input}' -> Score: {score_data['score']}, "
                    f"Comment: {score_data['comment']}"
                )

            except Exception as e:
                self.logger.exception(f"Error evaluating item: {item.input}")
                results["errors"].append({"input": item.input, "error": str(e)})

        # Calculate average score
        if evaluated_count > 0:
            results["average_score"] = total_score / evaluated_count

        self.logger.info(
            f"Evaluation completed. Average score: {results['average_score']:.3f} "
            f"({evaluated_count}/{results['total_items']} items evaluated)"
        )

        return results

    def extract_sequence(self, response: dict[str, Any]) -> list[dict[str, Any]]:
        """
        Extract the sequence of tool calls from a LangGraph response.

        Args:
            response: The response from LangGraph containing messages

        Returns:
            List of tool calls with tool_name and tool_args
        """
        messages = response.get("messages", [])
        tool_call_sequence = []

        for message in messages:
            # In LangChain, after a tool is called, the response is a ToolMessage.
            # The preceding AIMessage contains the tool_calls invocation.
            if isinstance(message, AIMessage) and message.tool_calls:
                for tool_call in message.tool_calls:
                    tool_call_sequence.append(
                        {
                            "tool_name": tool_call.get("name"),
                            "tool_args": tool_call.get("args"),
                        }
                    )

        return tool_call_sequence

    def evaluate_sequence(
        self,
        actual_sequence: list[dict[str, Any]],
        expected_sequence: list[dict[str, Any]],
    ) -> dict[str, Any]:
        """
        Compare an actual sequence of tool calls to an expected one.

        Args:
            actual_sequence: The actual sequence extracted from the response
            expected_sequence: The expected sequence from the dataset

        Returns:
            Dictionary with name, score, and comment
            - Score 1.0: Perfect match in order, tool names, and args
            - Score > 0: Partial match (num_correct_steps / num_expected_steps)
            - Score 0.0: First tool is wrong or sequence length mismatch
        """
        if len(actual_sequence) != len(expected_sequence):
            comment = (
                f"FAIL: Sequence length mismatch. "
                f"Expected {len(expected_sequence)}, got {len(actual_sequence)}."
            )
            return {"name": "tool_sequence_match", "score": 0, "comment": comment}

        if not expected_sequence:  # Handle the "no tool" case
            return {
                "name": "tool_sequence_match",
                "score": 1.0,
                "comment": "OK: Correctly called no tools.",
            }

        correct_steps = 0
        for i, expected_call in enumerate(expected_sequence):
            actual_call = actual_sequence[i]

            # 1. Check tool name
            if actual_call["tool_name"] != expected_call["tool_name"]:
                comment = (
                    f"FAIL at step {i + 1}: Incorrect tool. "
                    f"Expected '{expected_call['tool_name']}', "
                    f"got '{actual_call['tool_name']}'."
                )
                # Break on first major error for a stricter evaluation
                break

            # 2. Check tool args (simplified check for expected args subset)
            expected_args = expected_call.get("tool_args", {})
            actual_args = actual_call.get("tool_args", {})
            args_match = all(
                item in actual_args.items() for item in expected_args.items()
            )

            if not args_match:
                comment = (
                    f"FAIL at step {i + 1}: Argument mismatch for tool "
                    f"'{expected_call['tool_name']}'. "
                    f"Expected subset: {expected_args}, got {actual_args}."
                )
                break

            # If both name and args match, the step is correct
            correct_steps += 1

        score = correct_steps / len(expected_sequence)
        comment = (
            "OK"
            if score == 1.0
            else f"Partially correct: {correct_steps}/{len(expected_sequence)} steps matched."
        )

        return {"name": "tool_sequence_match", "score": score, "comment": comment}

    def _create_mock_response(self) -> dict[str, Any]:
        """
        Create a mock response for demonstration purposes.
        In production, this would be replaced with actual graph invocation.
        """
        return {
            "messages": [
                AIMessage(
                    content="",
                    tool_calls=[
                        {
                            "name": "search_deals",
                            "args": {
                                "project_name": "Project Titan",
                                "company_name": "Acme Corp",
                            },
                            "id": "1",
                        }
                    ],
                ),
                AIMessage(
                    content="",
                    tool_calls=[
                        {
                            "name": "get_salesforce_deal_status",
                            "args": {"deal_id": "some_generated_id"},
                            "id": "2",
                        }
                    ],
                ),
            ]
        }

    async def _invoke_graph(self, input_message: str, handler) -> dict[str, Any]:
        """
        Placeholder for actual graph invocation.
        This should be implemented to call your LangGraph application.

        Args:
            input_message: The input message to send to the graph
            handler: The Langfuse handler for tracking

        Returns:
            The response from the graph
        """
        # TODO: Implement actual graph invocation
        # Example:
        # response = await self.graph.ainvoke(
        #     {"messages": [("user", input_message)]},
        #     config={"callbacks": [handler]}
        # )
        # return response
        raise NotImplementedError("Graph invocation not yet implemented")
