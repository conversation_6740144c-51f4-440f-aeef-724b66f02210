from abc import ABC, abstractmethod
from typing import Any, Dict, List

from app.common.helpers.logger import get_logger

logger = get_logger()


class BaseEvaluationPipeline(ABC):
    """
    Abstract base class for evaluation pipelines.
    """

    def __init__(self, pipeline_name: str):
        self.pipeline_name = pipeline_name
        self.logger = logger

    @abstractmethod
    async def run_evaluation(self, dataset_name: str) -> Dict[str, Any]:
        """
        Run the evaluation pipeline on a given dataset.
        
        Args:
            dataset_name: Name of the dataset to evaluate against
            
        Returns:
            Dictionary containing evaluation results and metrics
        """
        pass

    @abstractmethod
    def extract_sequence(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract the sequence of actions/calls from a response.
        
        Args:
            response: The response from the system being evaluated
            
        Returns:
            List of extracted actions/calls
        """
        pass

    @abstractmethod
    def evaluate_sequence(
        self, 
        actual_sequence: List[Dict[str, Any]], 
        expected_sequence: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Evaluate an actual sequence against an expected sequence.
        
        Args:
            actual_sequence: The actual sequence extracted from the response
            expected_sequence: The expected sequence from the dataset
            
        Returns:
            Dictionary containing score, name, and comment
        """
        pass
