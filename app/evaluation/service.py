from typing import Any, Optional

from fastapi import BackgroundTasks
from langfuse import <PERSON><PERSON>

from app.common.helpers.logger import get_logger
from app.common.task_runner.runner import TaskRunner
from app.evaluation.pipelines.langfuse_evaluation import (
    LangfuseToolSequenceEvaluationPipeline,
)
from app.evaluation.schemas import (
    EvaluationResults,
    EvaluationTaskStatus,
    StartEvaluationRequest,
    StartEvaluationResponse,
)
from app.evaluation.tasks.evaluation_task import EvaluationTask

logger = get_logger()


class EvaluationService:
    """
    Service layer for managing evaluation operations.
    """

    def __init__(self, langfuse_client: Langfuse):
        self.langfuse_client = langfuse_client
        self.running_tasks: dict[str, EvaluationTask] = {}
        self.task_runners: dict[str, TaskRunner] = {}

    async def start_evaluation(
        self,
        request: StartEvaluationRequest,
        background_tasks: BackgroundTasks | None = None,
    ) -> StartEvaluationResponse:
        """
        Start an evaluation task.

        Args:
            request: Evaluation start request
            background_tasks: FastAPI BackgroundTasks for running evaluations in background

        Returns:
            Response with task information
        """
        logger.info(f"Starting evaluation for dataset: {request.dataset_name}")

        # Create evaluation task
        evaluation_task = EvaluationTask(
            dataset_name=request.dataset_name,
            langfuse_client=self.langfuse_client,
            interval_seconds=request.interval_seconds,
        )

        task_id = evaluation_task.task_id
        self.running_tasks[task_id] = evaluation_task

        if request.run_once:
            if background_tasks:
                # Use FastAPI BackgroundTasks for immediate background execution
                background_tasks.add_task(
                    self._run_background_evaluation, task_id, request.dataset_name
                )
                # Clean up task since it will run in background
                self.running_tasks.pop(task_id, None)

                return StartEvaluationResponse(
                    task_id=task_id,
                    dataset_name=request.dataset_name,
                    status="started_background",
                    message="Evaluation started in background. Check logs for results.",
                )
            else:
                # Run once synchronously and return results
                try:
                    result = await evaluation_task.execute_once()
                    # Clean up task since it's a one-time run
                    self.running_tasks.pop(task_id, None)

                    return StartEvaluationResponse(
                        task_id=task_id,
                        dataset_name=request.dataset_name,
                        status="completed",
                        message=f"Evaluation completed successfully. Average score: {result.get('evaluation_results', {}).get('average_score', 0.0):.3f}",
                    )
                except Exception as e:
                    # Clean up task on error
                    self.running_tasks.pop(task_id, None)
                    logger.exception(f"One-time evaluation failed: {e}")

                    return StartEvaluationResponse(
                        task_id=task_id,
                        dataset_name=request.dataset_name,
                        status="failed",
                        message=f"Evaluation failed: {str(e)}",
                    )
        else:
            # Start continuous evaluation
            task_runner = TaskRunner()
            task_runner.add_task(evaluation_task)
            self.task_runners[task_id] = task_runner

            # Start the task runner in daemon mode (non-blocking)
            # Note: This starts the task runner in the background
            # In a production environment, you might want to use a proper
            # background task queue like Celery or similar
            import asyncio

            asyncio.create_task(task_runner.start_daemon(wait=False))

            return StartEvaluationResponse(
                task_id=task_id,
                dataset_name=request.dataset_name,
                status="started",
                message=f"Continuous evaluation started with {request.interval_seconds}s interval",
            )

    async def stop_evaluation(self, task_id: str) -> dict[str, Any]:
        """
        Stop a running evaluation task.

        Args:
            task_id: ID of the task to stop

        Returns:
            Dictionary with stop operation result
        """
        if task_id not in self.running_tasks:
            return {"status": "error", "message": f"Task {task_id} not found"}

        # Stop the task
        task = self.running_tasks[task_id]
        task.stop()

        # Stop the task runner if it exists
        if task_id in self.task_runners:
            task_runner = self.task_runners[task_id]
            await task_runner.stop_daemon()
            self.task_runners.pop(task_id)

        # Remove from running tasks
        self.running_tasks.pop(task_id)

        logger.info(f"Stopped evaluation task: {task_id}")

        return {
            "status": "stopped",
            "task_id": task_id,
            "message": f"Task {task_id} stopped successfully",
        }

    def get_task_status(self, task_id: str) -> Optional[EvaluationTaskStatus]:
        """
        Get the status of a specific evaluation task.

        Args:
            task_id: ID of the task

        Returns:
            Task status or None if not found
        """
        if task_id not in self.running_tasks:
            return None

        task = self.running_tasks[task_id]
        status_dict = task.get_status()

        return EvaluationTaskStatus(**status_dict)

    def list_running_tasks(self) -> list[EvaluationTaskStatus]:
        """
        List all currently running evaluation tasks.

        Returns:
            List of task statuses
        """
        return [
            EvaluationTaskStatus(**task.get_status())
            for task in self.running_tasks.values()
        ]

    async def run_evaluation_once(self, dataset_name: str) -> EvaluationResults:
        """
        Run a single evaluation without creating a persistent task.

        Args:
            dataset_name: Name of the dataset to evaluate

        Returns:
            Evaluation results
        """
        logger.info(f"Running one-time evaluation for dataset: {dataset_name}")

        pipeline = LangfuseToolSequenceEvaluationPipeline(self.langfuse_client)
        results = await pipeline.run_evaluation(dataset_name)

        return EvaluationResults(**results)

    async def _run_background_evaluation(self, task_id: str, dataset_name: str) -> None:
        """
        Run evaluation in background using FastAPI BackgroundTasks.

        Args:
            task_id: ID of the evaluation task
            dataset_name: Name of the dataset to evaluate
        """
        try:
            logger.info(
                f"Starting background evaluation for dataset: {dataset_name} (task: {task_id})"
            )

            pipeline = LangfuseToolSequenceEvaluationPipeline(self.langfuse_client)
            results = await pipeline.run_evaluation(dataset_name)

            logger.info(
                f"Background evaluation completed for dataset: {dataset_name} (task: {task_id}). "
                f"Average score: {results.get('average_score', 0.0):.3f}, "
                f"Items evaluated: {results.get('evaluated_items', 0)}/{results.get('total_items', 0)}"
            )

        except Exception as e:
            logger.exception(
                f"Background evaluation failed for dataset: {dataset_name} (task: {task_id}): {e}"
            )
