from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ToolCall(BaseModel):
    """Schema for a tool call in a sequence."""
    tool_name: str = Field(..., description="Name of the tool that was called")
    tool_args: Dict[str, Any] = Field(default_factory=dict, description="Arguments passed to the tool")


class EvaluationScore(BaseModel):
    """Schema for an evaluation score result."""
    name: str = Field(..., description="Name of the evaluation metric")
    score: float = Field(..., ge=0.0, le=1.0, description="Score value between 0.0 and 1.0")
    comment: str = Field(..., description="Human-readable comment about the score")


class EvaluationItem(BaseModel):
    """Schema for a single evaluation item result."""
    input: str = Field(..., description="The input that was evaluated")
    score: float = Field(..., ge=0.0, le=1.0, description="Score for this item")
    comment: str = Field(..., description="Comment about this evaluation")


class EvaluationError(BaseModel):
    """Schema for an evaluation error."""
    input: str = Field(..., description="The input that caused the error")
    error: str = Field(..., description="Error message")


class EvaluationResults(BaseModel):
    """Schema for complete evaluation results."""
    dataset_name: str = Field(..., description="Name of the dataset that was evaluated")
    total_items: int = Field(..., ge=0, description="Total number of items in the dataset")
    evaluated_items: int = Field(..., ge=0, description="Number of items successfully evaluated")
    average_score: float = Field(..., ge=0.0, le=1.0, description="Average score across all evaluated items")
    scores: List[EvaluationItem] = Field(default_factory=list, description="Individual item scores")
    errors: List[EvaluationError] = Field(default_factory=list, description="Errors that occurred during evaluation")


class EvaluationTaskMetrics(BaseModel):
    """Schema for evaluation task metrics."""
    total_runs: int = Field(..., ge=0, description="Total number of evaluation runs")
    successful_runs: int = Field(..., ge=0, description="Number of successful runs")
    failed_runs: int = Field(..., ge=0, description="Number of failed runs")
    last_average_score: float = Field(..., ge=0.0, le=1.0, description="Average score from the last run")


class EvaluationTaskStatus(BaseModel):
    """Schema for evaluation task status."""
    id: str = Field(..., description="Task ID")
    name: str = Field(..., description="Task name")
    should_stop: bool = Field(..., description="Whether the task should stop")
    dataset_name: str = Field(..., description="Name of the dataset being evaluated")
    metrics: EvaluationTaskMetrics = Field(..., description="Task metrics")


class StartEvaluationRequest(BaseModel):
    """Schema for starting an evaluation."""
    dataset_name: str = Field(..., description="Name of the Langfuse dataset to evaluate")
    interval_seconds: Optional[int] = Field(
        default=3600, 
        ge=60, 
        description="Interval between evaluation runs in seconds (minimum 60)"
    )
    run_once: bool = Field(
        default=False, 
        description="Whether to run the evaluation once or continuously"
    )


class StartEvaluationResponse(BaseModel):
    """Schema for start evaluation response."""
    task_id: str = Field(..., description="ID of the started evaluation task")
    dataset_name: str = Field(..., description="Name of the dataset being evaluated")
    status: str = Field(..., description="Status of the task start operation")
    message: str = Field(..., description="Human-readable message about the operation")


class EvaluationExecutionResult(BaseModel):
    """Schema for evaluation execution result."""
    status: str = Field(..., description="Execution status (success/error)")
    dataset_name: str = Field(..., description="Name of the dataset that was evaluated")
    evaluation_results: Optional[EvaluationResults] = Field(
        default=None, 
        description="Evaluation results if successful"
    )
    error: Optional[str] = Field(default=None, description="Error message if failed")
    metrics: EvaluationTaskMetrics = Field(..., description="Task metrics")
