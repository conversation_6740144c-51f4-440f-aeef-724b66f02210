import uuid
from typing import Any, Dict

from langfuse import <PERSON><PERSON>

from app.common.helpers.logger import get_logger
from app.common.task_runner.base_task import BaseTask
from app.evaluation.pipelines.langfuse_evaluation import LangfuseToolSequenceEvaluationPipeline

logger = get_logger()


class EvaluationTask(BaseTask):
    """
    Background task for running evaluation pipelines.
    Extends BaseTask to integrate with the existing task runner framework.
    """

    def __init__(
        self,
        dataset_name: str,
        langfuse_client: Langfuse,
        interval_seconds: int = 3600,  # Default to 1 hour
        task_id: str | None = None,
    ):
        """
        Initialize the evaluation task.

        Args:
            dataset_name: Name of the Langfuse dataset to evaluate against
            langfuse_client: Configured Langfuse client
            interval_seconds: Interval between evaluation runs in seconds
            task_id: Unique identifier for this task
        """
        super().__init__(
            task_id=task_id or f"evaluation_task_{str(uuid.uuid4())[:8]}",
            interval_seconds=interval_seconds,
            logger=logger,
        )
        self.dataset_name = dataset_name
        self.langfuse_client = langfuse_client
        self.pipeline = LangfuseToolSequenceEvaluationPipeline(langfuse_client)
        self.metrics = {
            "total_runs": 0,
            "successful_runs": 0,
            "failed_runs": 0,
            "last_average_score": 0.0,
        }

    async def execute_once(self) -> Dict[str, Any]:
        """
        Execute one evaluation run.

        Returns:
            Dictionary with execution results and metrics
        """
        self.metrics["total_runs"] += 1
        
        try:
            self.logger.info(
                f"Starting evaluation run for dataset: {self.dataset_name}"
            )
            
            # Run the evaluation pipeline
            results = await self.pipeline.run_evaluation(self.dataset_name)
            
            # Update metrics
            self.metrics["successful_runs"] += 1
            self.metrics["last_average_score"] = results.get("average_score", 0.0)
            
            # Prepare execution result
            execution_result = {
                "status": "success",
                "dataset_name": self.dataset_name,
                "evaluation_results": results,
                "metrics": self.metrics.copy(),
            }
            
            self.logger.info(
                f"Evaluation completed successfully. "
                f"Average score: {results.get('average_score', 0.0):.3f}, "
                f"Items evaluated: {results.get('evaluated_items', 0)}/{results.get('total_items', 0)}"
            )
            
            return execution_result
            
        except Exception as e:
            self.metrics["failed_runs"] += 1
            error_msg = f"Evaluation failed: {str(e)}"
            
            self.logger.exception(error_msg)
            
            return {
                "status": "error",
                "dataset_name": self.dataset_name,
                "error": error_msg,
                "metrics": self.metrics.copy(),
            }

    def get_status(self) -> Dict[str, Any]:
        """Get detailed status information including evaluation metrics."""
        base_status = super().get_status()
        base_status.update({
            "dataset_name": self.dataset_name,
            "metrics": self.metrics.copy(),
        })
        return base_status
