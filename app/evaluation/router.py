from typing import Any

from fastapi import <PERSON><PERSON>out<PERSON>, BackgroundTasks, HTTPException, Path

from app.evaluation.dependencies import EvaluationServiceDep
from app.evaluation.schemas import (
    EvaluationResults,
    EvaluationTaskStatus,
    StartEvaluationRequest,
    StartEvaluationResponse,
)

router = APIRouter()


@router.post("/start", name="start_evaluation", response_model=StartEvaluationResponse)
async def start_evaluation(
    request: StartEvaluationRequest,
    background_tasks: BackgroundTasks,
    evaluation_service: EvaluationServiceDep,
):
    """
    Start an evaluation task for a Langfuse dataset.

    This endpoint can run evaluations either once or continuously:
    - If run_once=True: Runs the evaluation immediately in a background task
    - If run_once=False: Starts a persistent task that runs evaluations at the specified interval
    """
    return await evaluation_service.start_evaluation(request, background_tasks)


@router.post("/stop/{task_id}", name="stop_evaluation")
async def stop_evaluation(
    task_id: str = Path(..., description="ID of the evaluation task to stop"),
    evaluation_service: EvaluationServiceDep = None,
) -> dict[str, Any]:
    """
    Stop a running evaluation task.
    """
    return await evaluation_service.stop_evaluation(task_id)


@router.get(
    "/tasks", name="list_evaluation_tasks", response_model=list[EvaluationTaskStatus]
)
async def list_evaluation_tasks(
    evaluation_service: EvaluationServiceDep,
) -> list[EvaluationTaskStatus]:
    """
    List all currently running evaluation tasks.
    """
    return evaluation_service.list_running_tasks()


@router.get(
    "/tasks/{task_id}",
    name="get_evaluation_task_status",
    response_model=EvaluationTaskStatus,
)
async def get_evaluation_task_status(
    task_id: str = Path(..., description="ID of the evaluation task"),
    evaluation_service: EvaluationServiceDep = None,
) -> EvaluationTaskStatus:
    """
    Get the status of a specific evaluation task.
    """
    status = evaluation_service.get_task_status(task_id)
    if status is None:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
    return status


@router.post(
    "/run-once/{dataset_name}",
    name="run_evaluation_once",
    response_model=EvaluationResults,
)
async def run_evaluation_once(
    dataset_name: str = Path(
        ..., description="Name of the Langfuse dataset to evaluate"
    ),
    evaluation_service: EvaluationServiceDep = None,
) -> EvaluationResults:
    """
    Run a single evaluation without creating a persistent task.

    This is useful for ad-hoc evaluations or testing.
    Note: This runs synchronously and may take time for large datasets.
    """
    try:
        return await evaluation_service.run_evaluation_once(dataset_name)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Evaluation failed: {str(e)}")


@router.post(
    "/run-background/{dataset_name}",
    name="run_evaluation_background",
    response_model=StartEvaluationResponse,
)
async def run_evaluation_background(
    background_tasks: BackgroundTasks,
    evaluation_service: EvaluationServiceDep,
    dataset_name: str = Path(
        ..., description="Name of the Langfuse dataset to evaluate"
    ),
) -> StartEvaluationResponse:
    """
    Run a single evaluation in the background using FastAPI BackgroundTasks.

    This is ideal for evaluations that might take some time, as it returns immediately
    while the evaluation runs in the background. Check the logs for results.
    """
    from app.evaluation.schemas import StartEvaluationRequest

    request = StartEvaluationRequest(dataset_name=dataset_name, run_once=True)

    return await evaluation_service.start_evaluation(request, background_tasks)
