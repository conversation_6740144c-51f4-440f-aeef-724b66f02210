# Evaluation Module

This module provides a comprehensive evaluation framework for LangGraph tool call sequences using Langfuse. It's designed to evaluate whether agents use tools in the correct order and with the correct arguments.

## Architecture

The evaluation module follows the established patterns in the codebase:

- **Pipelines**: Core evaluation logic (`app/evaluation/pipelines/`)
- **Tasks**: Background task integration (`app/evaluation/tasks/`)
- **Service**: Business logic layer (`app/evaluation/service.py`)
- **Router**: FastAPI endpoints (`app/evaluation/router.py`)
- **Schemas**: Pydantic models (`app/evaluation/schemas.py`)
- **Dependencies**: Dependency injection (`app/evaluation/dependencies.py`)

## Features

### Tool Sequence Evaluation

The main evaluation pipeline (`LangfuseToolSequenceEvaluationPipeline`) evaluates:

1. **Tool Call Order**: Whether tools are called in the expected sequence
2. **Tool Names**: Whether the correct tools are called
3. **Tool Arguments**: Whether tools are called with the expected arguments

### Scoring System

- **Score 1.0**: Perfect match (correct order, names, and arguments)
- **Score 0.0-1.0**: Partial match (number of correct steps / total expected steps)
- **Score 0.0**: First tool wrong or sequence length mismatch

### Background Task Integration

The module provides two approaches for background execution:

#### 1. FastAPI BackgroundTasks (Recommended for one-time evaluations)

- **Use case**: Single evaluations that might take some time
- **Benefits**: Immediate API response, simple implementation
- **Limitations**: Tied to the request lifecycle, no persistence across server restarts

#### 2. Persistent Task Runner (For continuous evaluations)

- **Use case**: Continuous evaluation runs at regular intervals
- **Benefits**: Survives server restarts, configurable intervals, metrics tracking
- **Implementation**: Uses the existing `BaseTask` framework

## Usage

### 1. Setup Langfuse Dataset

Create a dataset in Langfuse with the following structure:

```json
{
  "input": "Find deals for Project Titan at Acme Corp and check their status",
  "expected_output": [
    {
      "tool_name": "search_deals",
      "tool_args": {
        "project_name": "Project Titan",
        "company_name": "Acme Corp"
      }
    },
    {
      "tool_name": "get_salesforce_deal_status",
      "tool_args": {
        "deal_id": "some_generated_id"
      }
    }
  ]
}
```

### 2. API Endpoints

#### Start Continuous Evaluation

```bash
POST /evaluation/start
{
  "dataset_name": "sales-agent-multi-tool-eval",
  "interval_seconds": 3600,
  "run_once": false
}
```

#### Run One-Time Evaluation (Synchronous)

```bash
POST /evaluation/run-once/sales-agent-multi-tool-eval
```

#### Run One-Time Evaluation (Background)

```bash
POST /evaluation/run-background/sales-agent-multi-tool-eval
```

_Returns immediately while evaluation runs in background. Check logs for results._

#### List Running Tasks

```bash
GET /evaluation/tasks
```

#### Get Task Status

```bash
GET /evaluation/tasks/{task_id}
```

#### Stop Evaluation

```bash
POST /evaluation/stop/{task_id}
```

### 3. Programmatic Usage

```python
from langfuse import Langfuse
from app.evaluation.service import EvaluationService
from app.evaluation.schemas import StartEvaluationRequest

# Create service
langfuse_client = Langfuse(
    public_key="your_public_key",
    secret_key="your_secret_key",
    host="https://cloud.langfuse.com"
)
service = EvaluationService(langfuse_client)

# Run one-time evaluation
results = await service.run_evaluation_once("your_dataset_name")

# Start continuous evaluation
request = StartEvaluationRequest(
    dataset_name="your_dataset_name",
    interval_seconds=3600,
    run_once=False
)
response = await service.start_evaluation(request)
```

## Integration with Existing Code

### Adding to Main Router

To integrate with your main FastAPI application, add the evaluation router:

```python
# In your main.py or router setup
from app.evaluation.router import router as evaluation_router

app.include_router(
    evaluation_router,
    prefix="/evaluation",
    tags=["evaluation"]
)
```

### Connecting to Your LangGraph

To evaluate your actual LangGraph application, modify the `_invoke_graph` method in `LangfuseToolSequenceEvaluationPipeline`:

```python
async def _invoke_graph(self, input_message: str, handler) -> Dict[str, Any]:
    """Invoke your actual LangGraph application."""
    # Replace this with your actual graph invocation
    response = await your_compiled_graph.ainvoke(
        {"messages": [("user", input_message)]},
        config={"callbacks": [handler]}
    )
    return response
```

## Testing

Run the tests using:

```bash
poetry run pytest app/evaluation/tests/ -v
```

The test suite covers:

- Tool sequence extraction
- Evaluation scoring logic
- Edge cases (empty sequences, mismatches)
- Pipeline execution

## Configuration

The module uses the existing Langfuse configuration from `app/core/config.py`:

- `LANGFUSE_PUBLIC_KEY`
- `LANGFUSE_SECRET_KEY`
- `LANGFUSE_HOST`

## Extending the Framework

### Custom Evaluation Pipelines

Create new evaluation pipelines by extending `BaseEvaluationPipeline`:

```python
class CustomEvaluationPipeline(BaseEvaluationPipeline):
    async def run_evaluation(self, dataset_name: str) -> Dict[str, Any]:
        # Your custom evaluation logic
        pass

    def extract_sequence(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        # Your custom extraction logic
        pass

    def evaluate_sequence(self, actual, expected) -> Dict[str, Any]:
        # Your custom scoring logic
        pass
```

### Custom Metrics

Add custom metrics to the evaluation task by extending `EvaluationTask` and modifying the `metrics` dictionary.

## Monitoring and Observability

The evaluation module provides:

- **Structured Logging**: All operations are logged with appropriate levels
- **Metrics Tracking**: Success/failure rates, average scores
- **Error Handling**: Graceful error handling with detailed error messages
- **Langfuse Integration**: All evaluations are tracked in Langfuse for analysis
