from unittest.mock import Mock

import pytest
from langchain_core.messages import AIMessage

from app.evaluation.pipelines.langfuse_evaluation import (
    LangfuseToolSequenceEvaluationPipeline,
)


@pytest.fixture
def mock_langfuse_client():
    """Mock Langfuse client for testing."""
    mock_client = Mock()
    mock_dataset = Mock()
    mock_dataset.items = []
    mock_client.get_dataset.return_value = mock_dataset
    return mock_client


@pytest.fixture
def evaluation_pipeline(mock_langfuse_client):
    """Create evaluation pipeline with mocked dependencies."""
    return LangfuseToolSequenceEvaluationPipeline(mock_langfuse_client)


class TestLangfuseToolSequenceEvaluationPipeline:
    """Test cases for the Langfuse tool sequence evaluation pipeline."""

    def test_extract_sequence_with_tool_calls(self, evaluation_pipeline):
        """Test extracting tool call sequence from response with tool calls."""
        response = {
            "messages": [
                AIMessage(
                    content="",
                    tool_calls=[
                        {
                            "name": "search_deals",
                            "args": {"project_name": "Project Titan"},
                            "id": "1",
                        }
                    ],
                ),
                AIMessage(
                    content="",
                    tool_calls=[
                        {
                            "name": "get_deal_status",
                            "args": {"deal_id": "123"},
                            "id": "2",
                        }
                    ],
                ),
            ]
        }

        sequence = evaluation_pipeline.extract_sequence(response)

        assert len(sequence) == 2
        assert sequence[0]["tool_name"] == "search_deals"
        assert sequence[0]["tool_args"] == {"project_name": "Project Titan"}
        assert sequence[1]["tool_name"] == "get_deal_status"
        assert sequence[1]["tool_args"] == {"deal_id": "123"}

    def test_extract_sequence_no_tool_calls(self, evaluation_pipeline):
        """Test extracting sequence from response with no tool calls."""
        response = {
            "messages": [AIMessage(content="Just a regular message", tool_calls=[])]
        }

        sequence = evaluation_pipeline.extract_sequence(response)

        assert len(sequence) == 0

    def test_evaluate_sequence_perfect_match(self, evaluation_pipeline):
        """Test evaluation with perfect sequence match."""
        actual = [
            {"tool_name": "search_deals", "tool_args": {"project": "Titan"}},
            {"tool_name": "get_status", "tool_args": {"id": "123"}},
        ]
        expected = [
            {"tool_name": "search_deals", "tool_args": {"project": "Titan"}},
            {"tool_name": "get_status", "tool_args": {"id": "123"}},
        ]

        result = evaluation_pipeline.evaluate_sequence(actual, expected)

        assert result["score"] == 1.0
        assert result["name"] == "tool_sequence_match"
        assert "OK" in result["comment"]

    def test_evaluate_sequence_length_mismatch(self, evaluation_pipeline):
        """Test evaluation with sequence length mismatch."""
        actual = [{"tool_name": "search_deals", "tool_args": {}}]
        expected = [
            {"tool_name": "search_deals", "tool_args": {}},
            {"tool_name": "get_status", "tool_args": {}},
        ]

        result = evaluation_pipeline.evaluate_sequence(actual, expected)

        assert result["score"] == 0
        assert "length mismatch" in result["comment"].lower()

    def test_evaluate_sequence_wrong_tool_name(self, evaluation_pipeline):
        """Test evaluation with wrong tool name."""
        actual = [{"tool_name": "wrong_tool", "tool_args": {}}]
        expected = [{"tool_name": "correct_tool", "tool_args": {}}]

        result = evaluation_pipeline.evaluate_sequence(actual, expected)

        assert result["score"] == 0
        assert "incorrect tool" in result["comment"].lower()

    def test_evaluate_sequence_partial_match(self, evaluation_pipeline):
        """Test evaluation with partial sequence match."""
        actual = [
            {"tool_name": "search_deals", "tool_args": {"project": "Titan"}},
            {"tool_name": "wrong_tool", "tool_args": {"id": "123"}},
        ]
        expected = [
            {"tool_name": "search_deals", "tool_args": {"project": "Titan"}},
            {"tool_name": "get_status", "tool_args": {"id": "123"}},
        ]

        result = evaluation_pipeline.evaluate_sequence(actual, expected)

        assert result["score"] == 0.5  # 1 out of 2 steps correct
        assert "partially correct" in result["comment"].lower()

    def test_evaluate_sequence_empty_sequences(self, evaluation_pipeline):
        """Test evaluation with empty sequences."""
        actual = []
        expected = []

        result = evaluation_pipeline.evaluate_sequence(actual, expected)

        assert result["score"] == 1.0
        assert "correctly called no tools" in result["comment"].lower()

    def test_evaluate_sequence_args_mismatch(self, evaluation_pipeline):
        """Test evaluation with argument mismatch."""
        actual = [{"tool_name": "search_deals", "tool_args": {"wrong": "args"}}]
        expected = [{"tool_name": "search_deals", "tool_args": {"project": "Titan"}}]

        result = evaluation_pipeline.evaluate_sequence(actual, expected)

        assert result["score"] == 0
        assert "argument mismatch" in result["comment"].lower()

    @pytest.mark.anyio
    async def test_run_evaluation_empty_dataset(
        self, evaluation_pipeline, mock_langfuse_client
    ):
        """Test running evaluation on empty dataset."""
        mock_dataset = Mock()
        mock_dataset.items = []
        mock_langfuse_client.get_dataset.return_value = mock_dataset

        results = await evaluation_pipeline.run_evaluation("test_dataset")

        assert results["dataset_name"] == "test_dataset"
        assert results["total_items"] == 0
        assert results["evaluated_items"] == 0
        assert results["average_score"] == 0.0
        assert len(results["scores"]) == 0
        assert len(results["errors"]) == 0
