from typing import Annotated

from fastapi import Depends
from langfuse import <PERSON><PERSON>

from app.core.config import config
from app.evaluation.service import EvaluationService


def get_langfuse_client() -> Langfuse:
    """
    Create and return a configured Langfuse client.
    
    Returns:
        Configured Langfuse client instance
    """
    return Langfuse(
        public_key=config.langfuse_public_key,
        secret_key=config.langfuse_secret_key,
        host=config.langfuse_host,
    )


LangfuseClientDep = Annotated[Langfuse, Depends(get_langfuse_client)]


def get_evaluation_service(
    langfuse_client: LangfuseClientDep,
) -> EvaluationService:
    """
    Create and return an evaluation service instance.
    
    Args:
        langfuse_client: Configured Langfuse client
        
    Returns:
        EvaluationService instance
    """
    return EvaluationService(langfuse_client)


EvaluationServiceDep = Annotated[EvaluationService, Depends(get_evaluation_service)]
